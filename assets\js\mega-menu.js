/**
 * Mega Menu JavaScript for DmrThema
 * Fare ile mega menu uzerinde gezinirken menunun kapanmasini onler
 */

document.addEventListener('DOMContentLoaded', function() {

    // Mega menu elementlerini sec
    const megaMenuItems = document.querySelectorAll('.main-navigation ul li.has-mega-menu');

    if (megaMenuItems.length === 0) {
        return; // Mega menu yoksa cik
    }
    
    // Her mega menu item icin event listener'lar ekle
    megaMenuItems.forEach(function(menuItem) {
        const subMenu = menuItem.querySelector('.sub-menu');

        if (!subMenu) {
            return; // Alt menu yoksa devam et
        }

        // Mega menu iceriginin olup olmadigini kontrol et
        const megaMenuContent = subMenu.querySelector('.mega-menu-page-content');
        if (!megaMenuContent) {
            return; // Mega menu icerigi yoksa normal menu gibi davransin
        }
        
        let hoverTimeout;
        
        // Menu item uzerine fare geldiginde
        menuItem.addEventListener('mouseenter', function() {
            // Timeout'u temizle (eger varsa)
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }

            // Diger acik mega menuleri kapat
            closeMegaMenus();

            // Bu mega menuyu ac - animasyon icin kisa gecikme
            requestAnimationFrame(function() {
                menuItem.classList.add('mega-menu-active');
            });
        });
        
        // Menu item'dan fare ciktiginda
        menuItem.addEventListener('mouseleave', function() {
            // Kisa bir gecikme ile menu kapat
            // Bu gecikme fareyi mega menu uzerine tasima firsati verir
            hoverTimeout = setTimeout(function() {
                menuItem.classList.remove('mega-menu-active');
            }, 150); // 150ms gecikme
        });
        
        // Sub menu uzerine fare geldiginde
        subMenu.addEventListener('mouseenter', function() {
            // Timeout'u temizle - menu acik kalsin
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }
            
            // Menu acik kalsin
            menuItem.classList.add('mega-menu-active');
        });
        
        // Sub menu'dan fare ciktiginda
        subMenu.addEventListener('mouseleave', function() {
            // Menu kapat
            menuItem.classList.remove('mega-menu-active');
        });
    });
    
    // Tum mega menuleri kapat
    function closeMegaMenus() {
        megaMenuItems.forEach(function(item) {
            item.classList.remove('mega-menu-active');
        });
    }
    
    // Sayfa uzerinde baska bir yere tiklandiginda mega menuleri kapat
    document.addEventListener('click', function(e) {
        // Tiklanilan element mega menu icinde degilse
        let isInsideMegaMenu = false;
        
        megaMenuItems.forEach(function(menuItem) {
            if (menuItem.contains(e.target)) {
                isInsideMegaMenu = true;
            }
        });
        
        if (!isInsideMegaMenu) {
            closeMegaMenus();
        }
    });
    
    // ESC tusuna basildiginda mega menuleri kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMegaMenus();
        }
    });
    
});
